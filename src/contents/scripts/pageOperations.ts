/**
 * 页面DOM操作相关的工具函数模块
 * 提供页面元素提取、插入、占位符处理等基础API
 */

import { blankNodeList, ELE_MIN_WIDTH, translateClassname } from '../const'

const { translateIcon, translationResult, splitParagraph, segmentWrapper, floatButtonTooltipTitle } = translateClassname

// 占位符映射接口
export interface PlaceholderMap {
  [key: string]: {
    tagName: string
    attributes: Record<string, string>
    content: string
  }
}

// 语义化HTML标签列表（需要保持结构的标签）
const SEMANTIC_TAGS = [
  'strong', 'b', 'em', 'i', 'span', 'a', 'code', 'mark',
  'u', 's', 'sub', 'sup', 'small', 'big', 'cite', 'q',
  'abbr', 'dfn', 'time', 'var', 'samp', 'kbd'
]

/**
 * 文本容器检查器 - 判断元素是否为文本容器
 */
export class TextContainerChecker {
  /**
   * 检查元素是否为文本容器元素
   * 文本容器元素是指直接包含文本内容的元素，而不是仅作为布局容器的div
   */
  static isTextContainer(element: HTMLElement): boolean {
    const tagName = element.tagName.toLowerCase()
    
    // 明确的文本容器标签
    const textContainerTags = ['p', 'span', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'td', 'th', 'label', 'button']
    if (textContainerTags.includes(tagName)) {
      return true
    }
    
    // 对于div，需要进一步判断是否为文本容器
    if (tagName === 'div') {
      return this.isDivTextContainer(element)
    }
    
    // 语义化标签也被视为文本容器
    if (SEMANTIC_TAGS.includes(tagName)) {
      return true
    }
    
    return false
  }
  
  /**
   * 判断div是否为文本容器
   * 如果div直接包含文本节点或只包含少量内联元素，则认为是文本容器
   */
  private static isDivTextContainer(div: HTMLElement): boolean {
    const childNodes = Array.from(div.childNodes)
    
    // 检查是否有直接的文本节点
    const hasDirectText = childNodes.some(node => 
      node.nodeType === Node.TEXT_NODE && node.textContent?.trim()
    )
    
    if (hasDirectText) {
      return true
    }
    
    // 检查子元素是否主要是内联元素
    const childElements = childNodes.filter(node => node.nodeType === Node.ELEMENT_NODE) as HTMLElement[]
    
    if (childElements.length === 0) {
      return false
    }
    
    // 如果子元素数量较少且主要是内联元素，则认为是文本容器
    if (childElements.length <= 3) {
      const inlineElements = childElements.filter(el => {
        const style = window.getComputedStyle(el)
        return style.display === 'inline' || style.display === 'inline-block' || SEMANTIC_TAGS.includes(el.tagName.toLowerCase())
      })
      
      return inlineElements.length === childElements.length
    }
    
    return false
  }
  
  /**
   * 检查元素是否应该被观察
   * 排除翻译相关元素和隐藏元素
   */
  static shouldObserveElement(element: HTMLElement): boolean {
    // 跳过黑名单标签
    if (blankNodeList.includes(element.tagName)) {
      return false
    }
    
    // 跳过翻译相关元素
    if (
      element.classList.contains(translateIcon) ||
      element.classList.contains(translationResult) ||
      element.classList.contains(splitParagraph) ||
      element.classList.contains(segmentWrapper) ||
      element.classList.contains(floatButtonTooltipTitle) ||
      element.closest(`.${floatButtonTooltipTitle}`)
    ) {
      return false
    }
    
    // 跳过隐藏元素
    if (this.isHidden(element)) {
      return false
    }
    
    // 检查元素宽度
    const clientWidth = element.getBoundingClientRect().width
    if (clientWidth <= ELE_MIN_WIDTH) {
      return false
    }
    
    return true
  }
  
  /**
   * 检查元素是否隐藏
   */
  private static isHidden(element: HTMLElement): boolean {
    const style = window.getComputedStyle(element)
    return (
      style.display === 'none' ||
      style.visibility === 'hidden' ||
      style.opacity === '0' ||
      element.hasAttribute('hidden')
    )
  }
}

/**
 * DOM操作工具类
 */
export class DomOperations {
  /**
   * 提取元素中的文本节点
   * 返回按段落分组的文本节点映射
   */
  static extractTextNodes(element: HTMLElement): Map<HTMLElement, Text[]> {
    const paragraphMap = new Map<HTMLElement, Text[]>()
    
    const walker = document.createTreeWalker(element, NodeFilter.SHOW_TEXT, {
      acceptNode: function (node) {
        // 过滤掉空白文本
        if (!node.textContent?.trim()) {
          return NodeFilter.FILTER_REJECT
        }
        
        // 过滤悬浮球 Tooltip 内的文本
        const parent = (node as Text).parentElement
        if (parent && (parent.classList.contains(floatButtonTooltipTitle) || parent.closest(`.${floatButtonTooltipTitle}`))) {
          return NodeFilter.FILTER_REJECT
        }
        
        return NodeFilter.FILTER_ACCEPT
      },
    })
    
    let node: Node | null
    while ((node = walker.nextNode())) {
      const textNode = node as Text
      const parentElement = textNode.parentElement
      
      if (!parentElement) {
        continue
      }
      
      // 检查是否在黑名单元素内
      const isBlankEle = blankNodeList.find((item) =>
        parentElement.closest(item.toLowerCase())
      )
      if (isBlankEle) {
        continue
      }
      
      // 避免处理翻译结果元素内的文本
      if (parentElement.closest(`.${translationResult}`) || parentElement.closest(`.${translateIcon}`)) {
        continue
      }
      
      // 检查元素宽度
      const clientWidth = parentElement.getBoundingClientRect().width
      if (clientWidth <= ELE_MIN_WIDTH) {
        continue
      }
      
      // 使用当前元素作为段落容器
      const paragraph = parentElement
      
      // 将文本节点添加到对应的段落映射中
      if (!paragraphMap.has(paragraph)) {
        paragraphMap.set(paragraph, [])
      }
      paragraphMap.get(paragraph)!.push(textNode)
    }
    
    return paragraphMap
  }
  
  /**
   * 将HTML内容转换为带占位符的文本
   */
  static convertHtmlToPlaceholders(htmlContent: string): {
    placeholderText: string
    placeholderMap: PlaceholderMap
  } {
    const placeholderMap: PlaceholderMap = {}
    let placeholderCounter = 0
    
    // 创建临时容器来解析HTML
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = htmlContent
    
    // 递归处理节点，将语义化标签替换为占位符
    function processNode(node: Node): string {
      if (node.nodeType === Node.TEXT_NODE) {
        return node.textContent || ''
      }
      
      if (node.nodeType === Node.ELEMENT_NODE) {
        const element = node as HTMLElement
        const tagName = element.tagName.toLowerCase()
        
        // 跳过图片等不需要翻译的元素
        if (blankNodeList.includes(element.tagName)) {
          return ''
        }
        
        // 如果是语义化标签，创建占位符
        if (SEMANTIC_TAGS.includes(tagName)) {
          const placeholderKey = `{${tagName}${placeholderCounter}}`
          const closingKey = `{/${tagName}${placeholderCounter}}`
          placeholderCounter++
          
          // 获取元素属性
          const attributes: Record<string, string> = {}
          for (let i = 0; i < element.attributes.length; i++) {
            const attr = element.attributes[i]
            attributes[attr.name] = attr.value
          }
          
          // 递归处理子节点获取内容
          const content = Array.from(element.childNodes)
            .map(child => processNode(child))
            .join('')
          
          // 存储占位符映射
          placeholderMap[placeholderKey] = {
            tagName,
            attributes,
            content
          }
          
          return `${placeholderKey}${content}${closingKey}`
        } else {
          // 对于其他元素，直接处理子节点
          return Array.from(element.childNodes)
            .map(child => processNode(child))
            .join('')
        }
      }
      
      return ''
    }
    
    const placeholderText = Array.from(tempDiv.childNodes)
      .map(node => processNode(node))
      .join('')
      .replace(/\s+/g, ' ')
      .trim()
    
    return { placeholderText, placeholderMap }
  }
  
  /**
   * 将带占位符的翻译文本转换回HTML结构
   */
  static convertPlaceholdersToHtml(
    translatedText: string,
    placeholderMap: PlaceholderMap
  ): string {
    let result = translatedText
    
    // 按占位符出现的顺序处理，确保嵌套结构正确
    const placeholderKeys = Object.keys(placeholderMap)
    
    // 处理每个占位符
    placeholderKeys.forEach(placeholderKey => {
      const mapping = placeholderMap[placeholderKey]
      const { tagName, attributes } = mapping
      
      // 构建开始标签
      let startTag = `<${tagName}`
      Object.entries(attributes).forEach(([name, value]) => {
        startTag += ` ${name}="${value}"`
      })
      startTag += '>'
      
      // 构建结束标签
      const endTag = `</${tagName}>`
      const closingKey = `{/${tagName}${placeholderKey.match(/\d+/)?.[0] || '0'}}`
      
      // 替换占位符对
      const placeholderPattern = new RegExp(
        `\\${placeholderKey}(.*?)\\${closingKey}`,
        'g'
      )
      
      result = result.replace(placeholderPattern, (_match, innerContent) => {
        return `${startTag}${innerContent}${endTag}`
      })
    })
    
    return result
  }
  
  /**
   * 插入翻译结果元素到指定位置
   */
  static insertTranslationResult(
    translationElement: HTMLElement,
    icon: HTMLElement,
    paragraph?: HTMLElement,
    segmentContainer?: HTMLElement
  ): void {
    // 将翻译结果插入到合适的位置
    if (segmentContainer && segmentContainer === icon) {
      // 特殊情况：segmentContainer就是图标本身，说明使用了新的无包装容器方案
      if (paragraph && (paragraph as any)._translationMap) {
        const translationMap = (paragraph as any)._translationMap as Map<HTMLElement, {
          segmentIndex: number
          segmentInfo: any
          insertionPoint: Node | null
        }>
        
        const mapInfo = translationMap.get(icon)
        if (mapInfo && mapInfo.insertionPoint) {
          // 在记录的插入点后面插入翻译结果
          if (mapInfo.insertionPoint.nextSibling) {
            paragraph.insertBefore(translationElement, mapInfo.insertionPoint.nextSibling)
          } else {
            paragraph.appendChild(translationElement)
          }
        } else {
          // 备用方案：插入到图标后面
          icon.parentNode?.insertBefore(translationElement, icon.nextSibling)
        }
      } else {
        // 备用方案：插入到图标后面
        icon.parentNode?.insertBefore(translationElement, icon.nextSibling)
      }
    } else if (segmentContainer && segmentContainer !== icon) {
      // 传统方案：有真正的分段容器
      if (segmentContainer.parentNode) {
        segmentContainer.parentNode.insertBefore(translationElement, segmentContainer.nextSibling)
      } else {
        // 备用方案：插入到图标后面
        icon.parentNode?.insertBefore(translationElement, icon.nextSibling)
      }
    } else if (paragraph) {
      // 否则插入到段落中
      paragraph.appendChild(translationElement)
    } else {
      // 如果没有段落信息，插入到图标后面
      icon.parentNode?.insertBefore(translationElement, icon.nextSibling)
    }
  }
  
  /**
   * 清理翻译文本中的转义字符
   */
  static cleanTranslationText(text: string): string {
    return (
      text
        // 处理转义字符，按正确顺序处理
        .replace(/\\"/g, '"') // 转义双引号 \" → "
        .replace(/\\'/g, "'") // 转义单引号 \' → '
        .replace(/\\n/g, ' ') // 转义换行符 \n → 空格
        .replace(/\\t/g, ' ') // 转义制表符 \t → 空格
        .replace(/\\r/g, ' ') // 转义回车符 \r → 空格
        .replace(/\\\\/g, '\\') // 转义反斜杠 \\ → \
        .replace(/\n/g, ' ') // 实际换行符 → 空格
        .replace(/\t/g, ' ') // 实际制表符 → 空格
        .replace(/\r/g, ' ') // 实际回车符 → 空格
        .replace(/\s+/g, ' ') // 多个空白字符 → 单个空格
        .trim()
    )
  }
}
