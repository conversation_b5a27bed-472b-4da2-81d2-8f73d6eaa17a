/**
 * 翻译页面节点
 */
import { MessageType } from '@src/common/const'
import { loadingIcon } from '@src/common/images'
import { translateClassname } from '../const'

import { buildApiUrl } from '@src/common/utils/baseUrl'
import { getUserId } from '@src/common/utils/userConfigApi'
import { PROJECTID } from '@src/config/aiChatConfig'
import {
  DomOperations,
  type PlaceholderMap,
  TextContainerChecker
} from './pageOperations'

const { translateIcon, translationResult, splitParagraph, segmentWrapper, floatButtonTooltipTitle } = translateClassname

// 全局翻译状态管理器
interface TranslationState {
  isPageTranslated: boolean
  isTranslating: boolean
  activeRequests: Map<string, AbortController>
}

// 声明全局类型
declare global {
  interface Window {
    __translationState?: TranslationState
  }
}

// 初始化全局翻译状态
function initGlobalTranslationState(): TranslationState {
  if (!window.__translationState) {
    window.__translationState = {
      isPageTranslated: false,
      isTranslating: false,
      activeRequests: new Map()
    }
  }
  return window.__translationState
}

// 获取全局翻译状态
function getGlobalTranslationState(): TranslationState {
  return initGlobalTranslationState()
}

// 设置页面翻译状态
function setPageTranslated(translated: boolean): void {
  const state = getGlobalTranslationState()
  state.isPageTranslated = translated

  // 触发状态变更事件，通知React组件更新
  window.dispatchEvent(new CustomEvent('translationStateChanged', {
    detail: { isPageTranslated: translated }
  }))
}

// 设置翻译进行状态
function setTranslating(translating: boolean): void {
  const state = getGlobalTranslationState()
  state.isTranslating = translating
}

// 添加活跃请求
function addActiveRequest(requestId: string, controller: AbortController): void {
  const state = getGlobalTranslationState()
  state.activeRequests.set(requestId, controller)
}

// 移除活跃请求
function removeActiveRequest(requestId: string): void {
  const state = getGlobalTranslationState()
  state.activeRequests.delete(requestId)
}

// 取消所有活跃请求
function cancelAllActiveRequests(): void {
  const state = getGlobalTranslationState()
  state.activeRequests.forEach((controller, requestId) => {
    console.log(`取消翻译请求: ${requestId}`)
    controller.abort()
  })
  state.activeRequests.clear()
}

// 全局变量管理
let textCollectionQueue: Array<{
  icon: HTMLElement
  textContent: string
  id: string
  paragraph: HTMLElement
  segmentContainer?: HTMLElement // 新增：段落分段容器
  htmlStructure?: Array<{
    type: 'text' | 'element'
    content: string
    tagName?: string
    attributes?: Record<string, string>
  }>
  // 新增：占位符相关字段
  placeholderText?: string
  placeholderMap?: PlaceholderMap
}> = []
let batchProcessTimer: NodeJS.Timeout | null = null
const BATCH_DELAY = 500 // 500ms延迟批量处理
const MIN_CHARS_FOR_BATCH = 500 // 少于2000字符时进行批量处理
let currentGroups = []

// 全局观察者管理
let globalIntersectionObserver: IntersectionObserver | null = null
let globalMutationObserver: MutationObserver | null = null

// 创建 Intersection Observer 实例
function createIntersectionObserver(): IntersectionObserver {
  return new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        // 在处理前检查翻译状态
        const state = getGlobalTranslationState()
        if (!state.isPageTranslated) {
          console.log('翻译已取消，跳过观察者回调')
          return
        }
        // 只处理可视区的元素
        if (entry.isIntersecting) {
          const targetElement = entry.target as HTMLElement
          // 跳过悬浮球 Tooltip 内的元素
          if (targetElement.closest(`.${floatButtonTooltipTitle}`)) {
            globalIntersectionObserver?.unobserve(targetElement)
            return
          }

          // 新的处理策略：只翻译进入视口的文本容器元素的直接内容
          processTextContainerElement(targetElement)

          // 一旦处理过，就不再观察这个元素
          globalIntersectionObserver?.unobserve(entry.target)
        }
      })
    },
    {
      root: null, // 相对于视口
      rootMargin: '100px', // 提前100px开始检测
      threshold: 0, // 元素进入可视区就触发
    }
  )
}

// 创建 Mutation Observer 实例
function createMutationObserver(): MutationObserver {
  return new MutationObserver((mutations) => {
    // 在处理前检查翻译状态
    const state = getGlobalTranslationState()
    if (!state.isPageTranslated) {
      console.log('翻译已取消，跳过变化观察者回调')
      return
    }

    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as HTMLElement

          // 避免处理翻译相关的元素
          if (
            element.classList.contains(translateIcon) ||
            element.classList.contains(translationResult) ||
            element.classList.contains(splitParagraph) ||
            element.classList.contains(segmentWrapper)
          ) {
            return
          }

          // 避免处理悬浮球 Tooltip 内的元素
          if (
            element.classList.contains(floatButtonTooltipTitle) ||
            element.closest(`.${floatButtonTooltipTitle}`)
          ) {
            return
          }

          // 避免处理翻译元素的子元素
          if (
            element.closest(`.${translateIcon}`) ||
            element.closest(`.${translationResult}`) ||
            element.closest(`.${splitParagraph}`) ||
            element.closest(`.${segmentWrapper}`)
          ) {
            return
          }

          // 对新添加的元素进行观察（使用新的观察策略）
          observerChildElement(element)
        }
      })
    })
  })
}

// 启动全局观察者
function startGlobalObserver(): void {
  console.log('启动全局观察者...')

  // 如果已有观察者，先停止
  stopGlobalObserver()

  // 创建新的观察者实例
  globalIntersectionObserver = createIntersectionObserver()
  globalMutationObserver = createMutationObserver()

  // 启动 MutationObserver 监听DOM变化
  globalMutationObserver.observe(document.body, {
    childList: true,
    subtree: true,
  })

  console.log('全局观察者已启动')
}

// 停止全局观察者
function stopGlobalObserver(): void {
  console.log('停止全局观察者...')

  // 停止 IntersectionObserver
  if (globalIntersectionObserver) {
    globalIntersectionObserver.disconnect()
    globalIntersectionObserver = null
    console.log('IntersectionObserver 已停止')
  }

  // 停止 MutationObserver
  if (globalMutationObserver) {
    globalMutationObserver.disconnect()
    globalMutationObserver = null
    console.log('MutationObserver 已停止')
  }

  console.log('全局观察者已停止')
}



// 处理文本容器元素（新的观察策略）
function processTextContainerElement(element: HTMLElement) {
  // 基础检查：是否应该处理该元素
  if (!TextContainerChecker.shouldObserveElement(element)) {
    return
  }

  // 避免处理已经有翻译结果的段落中的新增元素
  if (element.closest(`.${translationResult}`)) {
    return
  }
  // 避免处理已拆分的段落
  if (element.classList.contains(splitParagraph)) {
    return
  }
  // 避免处理分段包装容器
  if (element.classList.contains(segmentWrapper)) {
    return
  }
  // 避免处理已拆分段落中的元素
  if (element.closest(`.${splitParagraph}`)) {
    return
  }

  // 检查是否已经有翻译图标或结果
  if (
    element.querySelector(`.${translateIcon}`) ||
    element.querySelector(`.${translationResult}`) ||
    element.classList.contains(splitParagraph)
  ) {
    return
  }

  // 直接为该文本容器元素添加翻译图标
  addTranslateIcon(element)
}






// 生成唯一ID
function generateUniqueId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substring(2)
}

// 批量处理翻译
function processBatchTranslation() {
  if (textCollectionQueue.length === 0) return

  // 动态分组处理，确保每组字符数不超过MIN_CHARS_FOR_BATCH
  const groups = groupTextsByCharLimit(textCollectionQueue, MIN_CHARS_FOR_BATCH)
  currentGroups = groups

  console.log('发起请求 currentGroups', currentGroups);

  groups.forEach((group) => {
    if (group.length > 1) {
      // 批量处理
      const batchTexts = group.map((item) => item.textContent)
      const batchQuery = batchTexts.join('###')


      // callHiddenChatUIOnsend('text', `${batchQuery}`, {
      //   agentId: 'translate',
      //   extendParams: {
      //     msgChannel: 'translatePage',
      //   }
      // })
      handleTranslate(
        batchQuery,
        '',
        (response) => {


          if (response?.code === '0') {
            // 使用智能分割函数处理批量翻译结果
            // const results = splitBatchTranslationResult(response.result, group.length)
            const results = response.result.split('###')
            console.log('group.length >>> 1 response', response, group, results);
            group.forEach((item, index) => {
              if (results[index]) {
                updateIconWithTranslation(
                  item.icon,
                  results[index],
                  item.paragraph,
                  item.htmlStructure,
                  item.segmentContainer,
                  item.placeholderMap
                )
              }
            })
          }
        }
      )
    } else {
      // 单独处理
      const item = group[0]
      // callHiddenChatUIOnsend('text', `${item.textContent}`, {
      //   agentId: 'translate',
      //   extendParams: {
      //     msgChannel: 'translatePage',
      //   }
      // })
      handleTranslate(
        item.textContent,
        '',
        (response) => {
          console.log('group.length === 1 response', response);

          if (response?.code === '0') {
            updateIconWithTranslation(
              item.icon,
              response.result,
              item.paragraph,
              item.htmlStructure,
              item.segmentContainer,
              item.placeholderMap
            )
          }
        }
      )
    }
  })

  // 清空队列
  textCollectionQueue = []
}

// 处理翻译请求
const handleTranslate = async (query: string, _conversationID: string, sendResponse: (response: any) => void) => {
  const userId = await getUserId()
  const requestId = generateUniqueId()

  // 创建AbortController用于取消请求
  const abortController = new AbortController()
  addActiveRequest(requestId, abortController)

  try {
    // 在发起请求前检查翻译状态
    const state = getGlobalTranslationState()
    if (!state.isPageTranslated) {
      console.log('翻译已被取消，跳过请求:', requestId)
      removeActiveRequest(requestId)
      return
    }

    const chatResponse = await fetch(
      `${buildApiUrl('/chat/workflow/chrome-v2')}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          appId: "web-assistant",
          userId: userId,
          agentId: "translate",
          question: query,
          hideConversation: true,
          conversationId: '',
          projectId: PROJECTID
        }),
        signal: abortController.signal // 添加取消信号
      }
    )
    const chatRes = await chatResponse.text()
    console.log('翻译响应:', {
      query,
      chatRes
    })
    // 处理SSE流式数据，累积拼接所有翻译内容
    const lines = chatRes.split('\n\n').filter(line => line.trim().startsWith('data:'));
    let accumulatedTranslation = ''; // 累积的翻译结果
    let isStreamEnded = false; // 流是否结束

    for (const line of lines) {
      try {
        // 移除 'data:' 前缀并清理尾部换行符
        const jsonStr = line.replace(/^data:/, '').trim();
        // console.log('jsonStr', jsonStr);

        // 跳过空行或无效数据
        if (!jsonStr) continue;

        const jsonObj = JSON.parse(jsonStr);
        // console.log('jsonObj', jsonObj)

        // 检查是否是流结束标记
        if (jsonObj && jsonObj.msg === "stream end!") {
          isStreamEnded = true;
          console.log('检测到流结束标记');
          break; // 结束循环
        }

        // 检查是否是包含翻译结果的数据
        if (jsonObj && jsonObj.resultData && jsonObj.resultData.answerInfos) {
          const answerInfos = jsonObj.resultData.answerInfos;

          // 遍历 answerInfos 数组查找翻译结果
          for (const answerInfo of answerInfos) {
            if (answerInfo.text) {
              try {
                // 解析嵌套的 JSON 字符串
                const innerJson = JSON.parse(answerInfo.text);

                // 检查是否包含翻译内容
                if (innerJson.list && innerJson.list.length > 0) {
                  const content = innerJson.list[0].content;
                  if (content && content.text) {
                    // 累积翻译内容
                    accumulatedTranslation += content.text;
                    // console.log('累积翻译内容:', content.text);
                  }
                }
              } catch (innerParseError) {
                console.log('解析内层JSON失败:', innerParseError);
                continue;
              }
            }
          }
        }
      } catch (lineParseError) {
        console.log('解析行数据失败:', lineParseError);
        continue; // 继续处理下一行
      }
    }

    // 只有在流结束且有累积内容时才返回结果
    if (isStreamEnded && accumulatedTranslation.trim()) {
      // 在返回结果前再次检查翻译状态
      const currentState = getGlobalTranslationState()
      if (!currentState.isPageTranslated) {
        console.log('翻译已被取消，丢弃翻译结果:', requestId)
        removeActiveRequest(requestId)
        return
      }

      // 清理累积的翻译结果
      let finalTranslation = accumulatedTranslation
        .replace(/^翻译结果[：:]\s*/, '')
        .replace(/###Translation.*$/, '')
        .trim();

      console.log('最终翻译结果:', finalTranslation);

      // 移除请求记录
      removeActiveRequest(requestId)

      sendResponse({
        code: '0',
        result: finalTranslation,
      });
      return;
    }

    // 如果没有有效的翻译结果，返回错误
    console.log('未找到有效的翻译结果');
    removeActiveRequest(requestId)
    sendResponse({
      code: '1',
      error: '未找到有效的翻译结果',
    });
  } catch (error) {
    console.log('翻译错误:', error)
    removeActiveRequest(requestId)

    // 检查是否是取消错误
    if (error.name === 'AbortError') {
      console.log('翻译请求被取消:', requestId)
      return // 不调用sendResponse，避免错误回调
    }

    sendResponse({
      code: '1',
      error: error.message,
    })
  }
}

// 按字符数限制分组
function groupTextsByCharLimit(
  items: Array<{
    icon: HTMLElement
    textContent: string
    id: string
    paragraph: HTMLElement
    segmentContainer?: HTMLElement
    htmlStructure?: Array<{
      type: 'text' | 'element'
      content: string
      tagName?: string
      attributes?: Record<string, string>
    }>
    placeholderText?: string
    placeholderMap?: PlaceholderMap
  }>,
  maxChars: number
) {
  const groups: Array<
    Array<{
      icon: HTMLElement
      textContent: string
      id: string
      paragraph: HTMLElement
      segmentContainer?: HTMLElement
      htmlStructure?: Array<{
        type: 'text' | 'element'
        content: string
        tagName?: string
        attributes?: Record<string, string>
      }>
      placeholderText?: string
      placeholderMap?: PlaceholderMap
    }>
  > = []
  let currentGroup: Array<{
    icon: HTMLElement
    textContent: string
    id: string
    paragraph: HTMLElement
    segmentContainer?: HTMLElement
    htmlStructure?: Array<{
      type: 'text' | 'element'
      content: string
      tagName?: string
      attributes?: Record<string, string>
    }>
    placeholderText?: string
    placeholderMap?: PlaceholderMap
  }> = []
  let currentChars = 0

  items.forEach((item) => {
    const itemChars = item.textContent.length

    // 如果单个文本就超过限制，单独成组
    if (itemChars > maxChars) {
      if (currentGroup.length > 0) {
        groups.push(currentGroup)
        currentGroup = []
        currentChars = 0
      }
      groups.push([item])
      return
    }

    // 检查加入当前组是否会超过限制（包括分隔符的长度）
    const separatorChars = currentGroup.length > 0 ? 3 : 0 // ###的长度
    if (currentChars + separatorChars + itemChars <= maxChars) {
      currentGroup.push(item)
      currentChars += separatorChars + itemChars
    } else {
      // 当前组已满，开始新组
      if (currentGroup.length > 0) {
        groups.push(currentGroup)
      }
      currentGroup = [item]
      currentChars = itemChars
    }
  })

  // 添加最后一组
  if (currentGroup.length > 0) {
    groups.push(currentGroup)
  }

  return groups
}



// 重建HTML结构
function reconstructHtmlStructure(
  translatedText: string,
  htmlStructure: Array<{
    type: 'text' | 'element'
    content: string
    tagName?: string
    attributes?: Record<string, string>
  }>
): DocumentFragment {
  const fragment = document.createDocumentFragment()

  // 将翻译后的文本按空格分割
  const translatedWords = translatedText.split(/\s+/)
  let wordIndex = 0

  for (const item of htmlStructure) {
    if (item.type === 'text') {
      // 对于纯文本，计算应该占用多少个翻译后的词
      const originalWords = item.content.split(/\s+/)
      const wordsToTake = Math.min(
        originalWords.length,
        translatedWords.length - wordIndex
      )

      if (wordsToTake > 0) {
        const textNode = document.createTextNode(
          translatedWords.slice(wordIndex, wordIndex + wordsToTake).join(' ')
        )
        fragment.appendChild(textNode)
        wordIndex += wordsToTake
      }
    } else if (item.type === 'element' && item.tagName) {
      // 对于HTML元素，重建元素并填入翻译后的内容
      const element = document.createElement(item.tagName)

      // 恢复属性
      if (item.attributes) {
        Object.entries(item.attributes).forEach(([name, value]) => {
          element.setAttribute(name, value)
        })
      }

      // 计算这个元素应该占用多少个翻译后的词
      const originalWords = item.content.split(/\s+/)
      const wordsToTake = Math.min(
        originalWords.length,
        translatedWords.length - wordIndex
      )

      if (wordsToTake > 0) {
        element.textContent = translatedWords
          .slice(wordIndex, wordIndex + wordsToTake)
          .join(' ')
        wordIndex += wordsToTake
      }

      fragment.appendChild(element)
    }
  }

  // 如果还有剩余的翻译词汇，作为文本节点添加
  if (wordIndex < translatedWords.length) {
    const remainingText = translatedWords.slice(wordIndex).join(' ')
    if (remainingText.trim()) {
      fragment.appendChild(document.createTextNode(' ' + remainingText))
    }
  }

  return fragment
}

// 更新图标并创建翻译结果元素
function updateIconWithTranslation(
  icon: HTMLElement,
  translation: string,
  paragraph?: HTMLElement,
  htmlStructure?: Array<{
    type: 'text' | 'element'
    content: string
    tagName?: string
    attributes?: Record<string, string>
  }>,
  segmentContainer?: HTMLElement,
  placeholderMap?: PlaceholderMap
) {
  // 在插入翻译结果前检查翻译状态
  const state = getGlobalTranslationState()
  if (!state.isPageTranslated) {
    console.log('翻译已被取消，跳过插入翻译结果')
    // 隐藏图标但不插入翻译结果
    icon.style.display = 'none'
    return
  }

  // 使用新的清理函数处理翻译结果
  const cleanTranslation = DomOperations.cleanTranslationText(translation)
  console.log('翻译结果:', {
    translation,
    cleanTranslation,
    paragraph
  })

  // 获取原文内容进行比较
  let originalText = ''
  if (paragraph) {
    // 获取段落中的文本内容，排除已有的翻译结果
    const textNodes = Array.from(paragraph.childNodes)
      .filter(
        (node: HTMLElement) =>
          node.nodeType === Node.TEXT_NODE ||
          (node.nodeType === Node.ELEMENT_NODE &&
            !node.classList?.contains(translateIcon) &&
            !node.classList?.contains(translationResult))
      )
      .map((node) => node.textContent?.trim() || '')
      .join(' ')
      .replace(/\s+/g, ' ')
      .trim()
    originalText = textNodes
  }

  // 如果翻译结果和原文一致，则不创建新的翻译元素
  if (cleanTranslation === originalText) {
    // 直接隐藏图标，不添加翻译结果
    icon.style.display = 'none'
    return
  }

  // 隐藏加载图标
  icon.style.display = 'none'

  // 创建翻译结果元素
  const translationElement = document.createElement('div')
  translationElement.className = translationResult
  translationElement.style.cssText = `
    display: block;
    margin: 5px 0;
  `

  // 优先使用占位符机制重建HTML结构
  if (placeholderMap && Object.keys(placeholderMap).length > 0) {
    const reconstructedHtml = DomOperations.convertPlaceholdersToHtml(cleanTranslation, placeholderMap)
    translationElement.innerHTML = reconstructedHtml
  } else if (htmlStructure && htmlStructure.length > 0) {
    // 兼容旧的HTML结构信息
    const reconstructedFragment = reconstructHtmlStructure(
      cleanTranslation,
      htmlStructure
    )
    translationElement.appendChild(reconstructedFragment)
  } else {
    // 否则使用纯文本
    translationElement.innerText = cleanTranslation
  }

  // 将翻译结果插入到合适的位置
  DomOperations.insertTranslationResult(translationElement, icon, paragraph, segmentContainer)
}



// 添加带占位符的文本到翻译队列
function addToTranslationQueueWithPlaceholders(
  icon: HTMLElement,
  placeholderText: string,
  placeholderMap: PlaceholderMap,
  paragraph: HTMLElement,
  segmentContainer?: HTMLElement
) {
  const id = generateUniqueId()
  textCollectionQueue.push({
    icon,
    textContent: placeholderText, // 使用占位符文本作为翻译内容
    id,
    paragraph,
    segmentContainer,
    placeholderText,
    placeholderMap
  })

  // 重置定时器
  if (batchProcessTimer) {
    clearTimeout(batchProcessTimer)
  }

  // 设置批量处理定时器
  batchProcessTimer = setTimeout(() => {
    processBatchTranslation()
  }, BATCH_DELAY)
}



// 处理带占位符的翻译内容
async function handleTranslateTextContentWithPlaceholders(
  icon: HTMLElement,
  placeholderText: string,
  placeholderMap: PlaceholderMap,
  paragraph: HTMLElement,
  segmentContainer?: HTMLElement
) {
  // 添加到翻译队列，传递占位符信息
  addToTranslationQueueWithPlaceholders(icon, placeholderText, placeholderMap, paragraph, segmentContainer)
}



// 添加翻译图标
function addTranslateIcon(paragraph: HTMLElement, _textNodes?: Text[]) {
  addSingleTranslateIcon(paragraph)
}

// 为单个段落添加翻译图标（使用占位符机制）
function addSingleTranslateIcon(paragraph: HTMLElement) {
  const icon = document.createElement('span')
  icon.className = translateIcon
  icon.style.cssText = `
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-left: 4px;
    background-image: url(${loadingIcon});
    background-size: contain;
    background-repeat: no-repeat;
    vertical-align: middle;
  `

  // 使用占位符机制处理段落内容
  const { placeholderText, placeholderMap } = DomOperations.convertHtmlToPlaceholders(paragraph.innerHTML)

  // 将图标插入到段落末尾
  paragraph.appendChild(icon)

  // 处理翻译，传递占位符信息
  handleTranslateTextContentWithPlaceholders(
    icon,
    placeholderText,
    placeholderMap,
    paragraph
  )
}

function observerChildElement(element: HTMLElement) {
  if (!globalIntersectionObserver) {
    console.warn('IntersectionObserver 未初始化，跳过元素观察')
    return
  }

  // 新的观察策略：只观察文本容器元素
  const walker = document.createTreeWalker(
    element,
    NodeFilter.SHOW_ELEMENT,
    {
      acceptNode: function (node) {
        const element = node as HTMLElement

        // 检查是否应该观察该元素
        if (!TextContainerChecker.shouldObserveElement(element)) {
          return NodeFilter.FILTER_REJECT
        }

        // 只观察文本容器元素
        if (TextContainerChecker.isTextContainer(element)) {
          return NodeFilter.FILTER_ACCEPT
        }

        return NodeFilter.FILTER_SKIP // 跳过但继续遍历子元素
      }
    }
  )

  let node: Node | null
  while ((node = walker.nextNode())) {
    const textContainer = node as HTMLElement
    globalIntersectionObserver.observe(textContainer)
  }
}

const handleInsertDom = (data: any) => {
  console.log('handleInsertDom', {
    data,
    currentGroups
  });

  currentGroups.forEach((group: any) => {
    if (group.length > 1) {
      console.log('group.length > 1', {
        group
      });

      group.forEach((item: any, index: number) => {
        console.log('data[index]', {
          dataIndex: data[index],
          item
        });
        if (data[index]) {
          updateIconWithTranslation(
            item.icon,
            data[index],
            item.paragraph,
            item.htmlStructure,
            item.segmentContainer,
            item.placeholderMap
          )
        }
      })
    } else {
      const item = group[0]
      console.log('group.length == 1', {
        item,
        dataResult: data.result
      });

      updateIconWithTranslation(
        item.icon,
        data.results[0],
        item.paragraph,
        item.htmlStructure,
        item.segmentContainer,
        item.placeholderMap
      )
    }
  })
}



/**
 * 清理页面中所有翻译相关的元素
 */
function clearAllTranslationElements(): void {
  console.log('开始清理页面翻译元素...')

  // 清理所有翻译图标
  const translateIcons = document.querySelectorAll(`.${translateIcon}`)
  translateIcons.forEach(icon => {
    icon.remove()
  })

  // 清理所有翻译结果
  const translationResults = document.querySelectorAll(`.${translationResult}`)
  translationResults.forEach(result => {
    result.remove()
  })

  // 清理已拆分段落的标记
  const splitParagraphs = document.querySelectorAll(`.${splitParagraph}`)
  splitParagraphs.forEach(paragraph => {
    paragraph.classList.remove(splitParagraph)
    // 清理段落上的翻译映射信息
    if ((paragraph as any)._translationMap) {
      delete (paragraph as any)._translationMap
    }
  })

  // 清理分段包装容器
  const segmentWrappers = document.querySelectorAll(`.${segmentWrapper}`)
  segmentWrappers.forEach(wrapper => {
    // 将包装容器的内容移动到父元素中，然后删除包装容器
    const parent = wrapper.parentNode
    if (parent) {
      while (wrapper.firstChild) {
        parent.insertBefore(wrapper.firstChild, wrapper)
      }
      wrapper.remove()
    }
  })

  // 清空翻译队列
  textCollectionQueue = []

  // 清除批量处理定时器
  if (batchProcessTimer) {
    clearTimeout(batchProcessTimer)
    batchProcessTimer = null
  }

  // 重置当前分组
  currentGroups = []

  console.log('页面翻译元素清理完成')
}

/**
 * 取消页面翻译
 */
function cancelPageTranslation(): void {
  console.log('取消页面翻译...')

  // 1. 立即更新全局状态，阻止新的翻译请求和结果插入
  setPageTranslated(false)
  setTranslating(false)

  // 2. 停止所有观察者，防止新元素触发翻译
  stopGlobalObserver()

  // 3. 取消所有进行中的翻译请求
  cancelAllActiveRequests()

  // 4. 清理所有翻译元素
  clearAllTranslationElements()

  console.log('页面翻译已取消')
}

function handleStartTranslate() {
  console.log('开始页面翻译...')

  // 1. 设置全局翻译状态
  setPageTranslated(true)
  setTranslating(true)

  // 2. 启动全局观察者
  startGlobalObserver()

  // 3. 处理当前页面所有可见元素
  observerChildElement(document.body)

  console.log('页面翻译已启动')
}

// 监听来自sidepanel侧边栏的消息
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
  if (message.type === MessageType.CHECK_CONTENT_SCRIPT) {
    // 响应content script检查请求
    sendResponse({ loaded: true })
    return true
  }

  //侧栏中的翻译此页面会触发该事件
  if (message.type === MessageType.START_TRANSLATE) {
    // 直接开始翻译流程，跳过会话创建步骤
    console.log('收到START_TRANSLATE消息，开始页面翻译')

    // 确保全局状态在观察者启动前更新
    setPageTranslated(true)
    setTranslating(true)

    handleStartTranslate()
  }

  // 处理取消翻译消息
  if (message.type === MessageType.CANCEL_TRANSLATE) {
    console.log('injectTranslate: 收到取消翻译消息，开始执行取消操作')
    cancelPageTranslation()
    console.log('injectTranslate: 取消翻译操作完成')
  }

  // if (message.type === MessageType.INSERT_DOM) {
  //   //在这里处理插入元素
  //   handleInsertDom(message.data)
  // }
})

export {
  handleStartTranslate,
  handleInsertDom,
  cancelPageTranslation,
  getGlobalTranslationState,
  setPageTranslated,
  setTranslating,
  startGlobalObserver,
  stopGlobalObserver
}
