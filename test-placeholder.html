<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>占位符机制测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }

        .test-case {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .test-case h3 {
            margin-top: 0;
            color: #333;
        }

        .original,
        .placeholder,
        .translated,
        .final {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }

        .original {
            background-color: #f0f8ff;
        }

        .placeholder {
            background-color: #fff8dc;
        }

        .translated {
            background-color: #f0fff0;
        }

        .final {
            background-color: #ffe4e1;
        }

        code {
            background-color: #f4f4f4;
            padding: 2px 4px;
            border-radius: 2px;
            font-family: 'Courier New', monospace;
        }

        strong {
            color: #d63384;
        }

        .result {
            font-weight: bold;
        }

        #real-content {
            border: 2px solid #007bff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            background-color: #f8f9fa;
        }

        .test-paragraph {
            border: 1px solid #ccc;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            background-color: white;
        }

        .test-paragraph::before {
            content: "段落 " attr(data-test) ": ";
            font-weight: bold;
            color: #007bff;
            display: block;
            margin-bottom: 5px;
        }

        .emoji {
            width: 16px;
            height: 16px;
            vertical-align: middle;
        }

        .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 2px;
        }
    </style>
</head>

<body>
    <h1>占位符机制测试</h1>
    <p>这个页面用于测试HTML标签占位符机制，模拟翻译过程中保持HTML结构的功能。</p>

    <!-- 真实DOM测试内容 -->
    <div id="real-content">
        <h2>真实DOM测试内容</h2>
        <p class="test-paragraph" data-test="1">
            To get the <strong data-doubao-translate-traverse-mark="1">best results</strong> from AI-generated prompts,
            follow these <strong data-doubao-translate-traverse-mark="1">best practices</strong>.
        </p>
        <p class="test-paragraph" data-test="2">
            <img draggable="false" role="img" class="emoji" alt="✔"
                src="https://s.w.org/image/core/emoji/15.0.3/svg/2714.svg" data-doubao-translate-traverse-mark="1">
            <strong data-doubao-translate-traverse-mark="1">Test and refine</strong> — Run the prompt and tweak if
            necessary.
            <br data-doubao-translate-traverse-mark="1">
            <img draggable="false" role="img" class="emoji" alt="✔"
                src="https://s.w.org/image/core/emoji/15.0.3/svg/2714.svg" data-doubao-translate-traverse-mark="1">
            <strong data-doubao-translate-traverse-mark="1">Use examples</strong> — If possible, provide a sample
            format.
            <br data-doubao-translate-traverse-mark="1">
            <img draggable="false" role="img" class="emoji" alt="✔"
                src="https://s.w.org/image/core/emoji/15.0.3/svg/2714.svg" data-doubao-translate-traverse-mark="1">
            <strong data-doubao-translate-traverse-mark="1">Be concise yet detailed</strong> — Provide enough details
            but avoid unnecessary complexity.
            <br data-doubao-translate-traverse-mark="1">
            <img draggable="false" role="img" class="emoji" alt="✔"
                src="https://s.w.org/image/core/emoji/15.0.3/svg/2714.svg" data-doubao-translate-traverse-mark="1">
            <strong data-doubao-translate-traverse-mark="1">Specify tone & format</strong> — (e.g., "Write in a
            professional tone, max 200 words").
            <br data-doubao-translate-traverse-mark="1">
            Our AI Prompt Generator <strong data-doubao-translate-traverse-mark="1">ensures</strong> these best
            practices are followed automatically.
        </p>
        <p class="test-paragraph" data-test="3">
            <span class="highlight">普通文本<strong>重要文本</strong>更多文本</span> 和 <em>斜体文本</em> 以及 <code>代码文本</code>。
        </p>
        <div class="test-paragraph" data-test="4">
            Our AI Prompt Generator <strong data-doubao-translate-traverse-mark="1">ensures</strong> these best
            practices are followed automatically.
        </div>
    </div>

    <!-- 测试结果显示区域 -->
    <div id="test-results"></div>

    <script>
        // 占位符映射接口
        const SEMANTIC_TAGS = [
            'strong', 'b', 'em', 'i', 'span', 'a', 'code', 'mark',
            'u', 's', 'sub', 'sup', 'small', 'big', 'cite', 'q',
            'abbr', 'dfn', 'time', 'var', 'samp', 'kbd'
        ];

        const blankNodeList = [
            'SCRIPT', 'STYLE', 'LINK', 'SVG', 'KBD', 'PRE', 'IMG', 'PATH',
            'VIDEO', 'AUDIO', 'SOURCE', 'CANVAS', 'IFRAME', 'CODE', 'FOOTER', 'NAV'
        ];

        /**
         * 将HTML内容转换为带占位符的文本
         */
        function convertHtmlToPlaceholders(htmlContent) {
            const placeholderMap = {};
            let placeholderCounter = 0;

            // 创建临时容器来解析HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = htmlContent;

            // 递归处理节点，将语义化标签替换为占位符
            function processNode(node) {
                if (node.nodeType === Node.TEXT_NODE) {
                    return node.textContent || '';
                }

                if (node.nodeType === Node.ELEMENT_NODE) {
                    const element = node;
                    const tagName = element.tagName.toLowerCase();

                    // 跳过图片等不需要翻译的元素
                    if (blankNodeList.includes(element.tagName)) {
                        return '';
                    }

                    // 如果是语义化标签，创建占位符
                    if (SEMANTIC_TAGS.includes(tagName)) {
                        const placeholderKey = `{${tagName}${placeholderCounter}}`;
                        const closingKey = `{/${tagName}${placeholderCounter}}`;
                        placeholderCounter++;

                        // 获取元素属性
                        const attributes = {};
                        for (let i = 0; i < element.attributes.length; i++) {
                            const attr = element.attributes[i];
                            attributes[attr.name] = attr.value;
                        }

                        // 递归处理子节点获取内容
                        const content = Array.from(element.childNodes)
                            .map(child => processNode(child))
                            .join('');

                        // 存储占位符映射
                        placeholderMap[placeholderKey] = {
                            tagName,
                            attributes,
                            content
                        };

                        return `${placeholderKey}${content}${closingKey}`;
                    } else {
                        // 对于其他元素，直接处理子节点
                        return Array.from(element.childNodes)
                            .map(child => processNode(child))
                            .join('');
                    }
                }

                return '';
            }

            const placeholderText = Array.from(tempDiv.childNodes)
                .map(node => processNode(node))
                .join('')
                .replace(/\s+/g, ' ')
                .trim();

            return { placeholderText, placeholderMap };
        }

        /**
         * 将带占位符的翻译文本转换回HTML结构
         */
        function convertPlaceholdersToHtml(translatedText, placeholderMap) {
            let result = translatedText;

            // 按占位符出现的顺序处理，确保嵌套结构正确
            const placeholderKeys = Object.keys(placeholderMap);

            // 处理每个占位符
            placeholderKeys.forEach(placeholderKey => {
                const mapping = placeholderMap[placeholderKey];
                const { tagName, attributes } = mapping;

                // 构建开始标签
                let startTag = `<${tagName}`;
                Object.entries(attributes).forEach(([name, value]) => {
                    startTag += ` ${name}="${value}"`;
                });
                startTag += '>';

                // 构建结束标签
                const endTag = `</${tagName}>`;
                const closingKey = `{/${tagName}${placeholderKey.match(/\d+/)?.[0] || '0'}}`;

                // 替换占位符对
                const placeholderPattern = new RegExp(
                    `\\${placeholderKey}(.*?)\\${closingKey}`,
                    'g'
                );

                result = result.replace(placeholderPattern, (match, innerContent) => {
                    return `${startTag}${innerContent}${endTag}`;
                });
            });

            return result;
        }

        // 模拟翻译API
        function simulateTranslation(placeholderText) {
            // 简单的翻译映射
            const translations = {
                'To get the {strong0}best results{/strong0} from AI-generated prompts, follow these {strong1}best practices{/strong1}.':
                    '要从AI生成的提示中获得{strong0}最佳结果{/strong0}，请遵循这些{strong1}最佳实践{/strong1}。',
                '{strong0}Test and refine{/strong0} — Run the prompt and tweak if necessary.':
                    '{strong0}测试和完善{/strong0} — 运行提示并根据需要进行调整。',
                '{strong0}Use examples{/strong0} — If possible, provide a sample format.':
                    '{strong0}使用示例{/strong0} — 如果可能，提供示例格式。',
                '{strong0}Be concise yet detailed{/strong0} — Provide enough details but avoid unnecessary complexity.':
                    '{strong0}简洁而详细{/strong0} — 提供足够的细节，但避免不必要的复杂性。',
                '{strong0}Specify tone & format{/strong0} — (e.g., "Write in a professional tone, max 200 words").':
                    '{strong0}指定语调和格式{/strong0} — （例如，"以专业语调写作，最多200字"）。',
                'Our AI Prompt Generator {strong0}ensures{/strong0} these best practices are followed automatically.':
                    '我们的AI提示生成器{strong0}确保{/strong0}自动遵循这些最佳实践。',
                '{span0}普通文本{strong1}重要文本{/strong1}更多文本{/span0} 和 {em2}斜体文本{/em2} 以及 {code3}代码文本{/code3}。':
                    '{span0}Normal text{strong1}Important text{/strong1}More text{/span0} and {em2}italic text{/em2} and {code3}code text{/code3}.'
            };

            return translations[placeholderText] || placeholderText + ' [翻译]';
        }

        // 按br标签分割段落内容
        function splitByBrTags(htmlContent) {
            // 移除翻译相关的元素（模拟实际场景）
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = htmlContent;

            // 按br标签分割内容
            const parts = htmlContent.split(/<br\s*\/?>/i);
            const segments = [];

            parts.forEach((part, index) => {
                const trimmedPart = part.trim();
                if (trimmedPart) {
                    // 使用占位符机制处理HTML内容
                    const { placeholderText, placeholderMap } = convertHtmlToPlaceholders(trimmedPart);

                    // 创建临时元素来获取纯文本内容
                    const tempElement = document.createElement('div');
                    tempElement.innerHTML = trimmedPart;
                    const textContent = tempElement.textContent?.trim() || '';

                    if (textContent) {
                        segments.push({
                            index: index + 1,
                            htmlContent: trimmedPart,
                            textContent,
                            placeholderText,
                            placeholderMap
                        });
                    }
                }
            });

            return segments;
        }

        // 处理单个段落的翻译
        function processTestParagraph(paragraph, index) {
            const originalHtml = paragraph.innerHTML;
            console.log(`\n=== 处理段落 ${index + 1} ===`);
            console.log('原始HTML:', originalHtml);

            // 检查是否包含br标签
            const hasBrTags = originalHtml.includes('<br');

            if (hasBrTags) {
                console.log('检测到br标签，进行分段处理');
                const segments = splitByBrTags(originalHtml);
                console.log('分段结果:', segments);

                return {
                    originalHtml,
                    hasBrTags: true,
                    segments: segments.map(segment => ({
                        ...segment,
                        translatedText: simulateTranslation(segment.placeholderText),
                        finalHtml: convertPlaceholdersToHtml(simulateTranslation(segment.placeholderText), segment.placeholderMap)
                    }))
                };
            } else {
                // 1. 转换为占位符
                const { placeholderText, placeholderMap } = convertHtmlToPlaceholders(originalHtml);
                console.log('占位符文本:', placeholderText);
                console.log('占位符映射:', placeholderMap);

                // 2. 模拟翻译
                const translatedText = simulateTranslation(placeholderText);
                console.log('翻译后文本:', translatedText);

                // 3. 转换回HTML
                const finalHtml = convertPlaceholdersToHtml(translatedText, placeholderMap);
                console.log('最终HTML:', finalHtml);

                return {
                    originalHtml,
                    hasBrTags: false,
                    placeholderText,
                    placeholderMap,
                    translatedText,
                    finalHtml
                };
            }
        }

        // 运行测试
        function runTests() {
            const resultsContainer = document.getElementById('test-results');
            const testParagraphs = document.querySelectorAll('.test-paragraph');

            console.log('=== 开始DOM提取和翻译测试 ===');

            testParagraphs.forEach((paragraph, index) => {
                const result = processTestParagraph(paragraph, index);

                console.log(
                    'result', result
                );


                // 创建测试结果显示
                const testDiv = document.createElement('div');
                testDiv.className = 'test-case';

                if (result.hasBrTags) {
                    // 处理分段的情况
                    let segmentsHtml = '';
                    result.segments.forEach((segment, segIndex) => {
                        segmentsHtml += `
                            <div style="border: 1px solid #ddd; margin: 10px 0; padding: 10px; border-radius: 4px;">
                                <h4>分段 ${segment.index}</h4>
                                <div class="original">
                                    <strong>原始HTML:</strong><br>
                                    <code>${escapeHtml(segment.htmlContent)}</code><br>
                                    <div class="result">渲染结果: ${segment.htmlContent}</div>
                                </div>
                                <div class="placeholder">
                                    <strong>占位符文本:</strong><br>
                                    <code>${escapeHtml(segment.placeholderText)}</code><br>
                                    <strong>占位符映射:</strong><br>
                                    <code>${escapeHtml(JSON.stringify(segment.placeholderMap, null, 2))}</code>
                                </div>
                                <div class="translated">
                                    <strong>翻译后的占位符文本:</strong><br>
                                    <code>${escapeHtml(segment.translatedText)}</code>
                                </div>
                                <div class="final">
                                    <strong>最终HTML:</strong><br>
                                    <code>${escapeHtml(segment.finalHtml)}</code><br>
                                    <div class="result">渲染结果: ${segment.finalHtml}</div>
                                </div>
                            </div>
                        `;
                    });

                    testDiv.innerHTML = `
                        <h3>测试段落 ${index + 1} (包含br标签分段)</h3>
                        <div class="original">
                            <strong>完整原始HTML:</strong><br>
                            <code>${escapeHtml(result.originalHtml)}</code><br>
                            <div class="result">渲染结果: ${result.originalHtml}</div>
                        </div>
                        <h4>分段处理结果:</h4>
                        ${segmentsHtml}
                    `;
                } else {
                    // 处理单个段落的情况
                    testDiv.innerHTML = `
                        <h3>测试段落 ${index + 1}</h3>
                        <div class="original">
                            <strong>原始HTML:</strong><br>
                            <code>${escapeHtml(result.originalHtml)}</code><br>
                            <div class="result">渲染结果: ${result.originalHtml}</div>
                        </div>
                        <div class="placeholder">
                            <strong>占位符文本:</strong><br>
                            <code>${escapeHtml(result.placeholderText)}</code><br>
                            <strong>占位符映射:</strong><br>
                            <code>${escapeHtml(JSON.stringify(result.placeholderMap, null, 2))}</code>
                        </div>
                        <div class="translated">
                            <strong>翻译后的占位符文本:</strong><br>
                            <code>${escapeHtml(result.translatedText)}</code>
                        </div>
                        <div class="final">
                            <strong>最终HTML:</strong><br>
                            <code>${escapeHtml(result.finalHtml)}</code><br>
                            <div class="result">渲染结果: ${result.finalHtml}</div>
                        </div>
                    `;
                }

                resultsContainer.appendChild(testDiv);
            });
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>

</html>